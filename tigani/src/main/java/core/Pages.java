package core;

/**
 *
 * <AUTHOR>
 */
public class Pages {

    // errors
    public static final String ERROR_404                    = "fe/404.html";

    // login
    public static final String BE_LOGIN                     = "be/login.html";
    
    // forgot
    public static final String BE_FORGOT                    = "be/forgot.html";
    
    // dashboard
    public static final String BE_DASHBOARD                 = "be/dashboard.html";
    
    // manteinance / tables
    public static final String BE_TABLE_COLLECTION          = "be/table-collection.html";

    
    // ========================
    // old
    
    // settings
    public static final String BE_SETTINGS_COMPANY          = "be/settings-company.html";

    public static final String BE_SETTINGS_SMTP_COLLECTION  = "be/settings-smtp-collection.html";
    public static final String BE_SETTINGS_SMTP             = "be/settings-smtp.html";

    public static final String BE_SETTINGS_USER_COLLECTION  = "be/settings-user-collection.html";
    public static final String BE_SETTINGS_USER             = "be/settings-user.html";

    // user
    public static final String BE_USER_COLLECTION           = "be/user-collection.html";
    public static final String BE_USER                      = "be/user.html";
    public static final String BE_USER_FORM                 = "be/user-form.html";
    public static final String BE_USER_PERMISSIONS_MANAGER  = "be/user-permissions-manager.html";

    // contact
    public static final String BE_CONTACT_COLLECTION       = "be/contact-collection.html";
    public static final String BE_CONTACT                  = "be/contact.html";
    public static final String BE_CONTACT_FORM             = "be/contact-form.html";

    // country
    public static final String BE_COUNTRY_COLLECTION       = "be/country-collection.html";
    public static final String BE_COUNTRY                  = "be/country.html";
    public static final String BE_COUNTRY_FORM             = "be/include/snippets/pojo/country-form.html";

    // dealer
    public static final String BE_DEALER_COLLECTION         = "be/dealer-collection.html";
    public static final String BE_DEALER_VIEW               = "be/dealer-view.html";
    public static final String BE_DEALER_FORM               = "be/dealer-form.html";

    // insurance company
    public static final String BE_INSURANCECOMPANY_COLLECTION = "be/insurancecompany-collection.html";
    public static final String BE_INSURANCECOMPANY         = "be/insurancecompany.html";

    // warranty
    public static final String BE_WARRANTY_COLLECTION      = "be/warranty-collection.html";
    public static final String BE_WARRANTY                 = "be/warranty.html";
    public static final String BE_WARRANTY_CRITERIA_TEST   = "be/warranty-criteria-test.html";

    // warranty details
    public static final String BE_WARRANTYDETAILS_COLLECTION = "be/warrantydetails-collection.html";
    public static final String BE_WARRANTYDETAILS          = "be/warrantydetails.html";

    // channel
    public static final String BE_CHANNEL_COLLECTION       = "be/channel-collection.html";
    public static final String BE_CHANNEL                  = "be/channel.html";

    // warranty type
    public static final String BE_WARRANTYTYPE_COLLECTION  = "be/warrantytype-collection.html";
    public static final String BE_WARRANTYTYPE             = "be/warrantytype.html";

    // insurance provenance type
    public static final String BE_INSURANCEPROVENANCETYPE_COLLECTION = "be/insuranceprovenancetype-collection.html";
    public static final String BE_INSURANCEPROVENANCETYPE  = "be/insuranceprovenancetype.html";

    // field translation
    public static final String BE_FIELDTRANSLATION_COLLECTION = "be/fieldtranslation-collection.html";
    public static final String BE_FIELDTRANSLATION = "be/fieldtranslation.html";

    // mailtemplate
    public static final String BE_MAILTEMPLATE_COLLECTION   = "be/mailtemplate-collection.html";
    public static final String BE_MAILTEMPLATE              = "be/mailtemplate.html";

    // import
    public static final String BE_IMPORT                    = "be/import.html";

}
